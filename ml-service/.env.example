# FinSync360 ML Service Environment Configuration

# Application Settings
DEBUG=true
HOST=0.0.0.0
PORT=8001
ENVIRONMENT=development

# Security
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database Configuration
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=finsync360

# Backend API Integration
BACKEND_API_URL=http://localhost:5000/api
BACKEND_API_KEY=optional-api-key-for-backend-communication

# CORS Settings (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000,http://localhost:8000
ALLOWED_HOSTS=localhost,127.0.0.1,*

# ML Model Configuration
MODEL_STORAGE_PATH=./models/saved
MODEL_RETRAIN_INTERVAL_HOURS=24
MIN_TRAINING_DATA_SIZE=100

# Prediction Settings
PAYMENT_PREDICTION_DAYS_AHEAD=30
INVENTORY_FORECAST_DAYS_AHEAD=90
RISK_ASSESSMENT_THRESHOLD=0.7

# Redis Configuration (for caching and task queue)
REDIS_URL=redis://localhost:6379/0
CACHE_TTL_SECONDS=3600

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Feature Engineering Settings
FEATURE_WINDOW_DAYS=365
SEASONAL_PERIODS=7,30,90,365

# Model Performance Thresholds
MIN_MODEL_ACCURACY=0.75
MIN_MODEL_PRECISION=0.70
MIN_MODEL_RECALL=0.70

# Business Rules
HIGH_RISK_CUSTOMER_THRESHOLD=0.8
LOW_STOCK_THRESHOLD_DAYS=7
OVERSTOCK_THRESHOLD_MULTIPLIER=3.0
