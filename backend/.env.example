# Environment Configuration
NODE_ENV=development
PORT=5000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/finsync360
MONGODB_TEST_URI=mongodb://localhost:27017/finsync360_test

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRE=7d
JWT_REFRESH_EXPIRE=30d

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key

# Payment Gateway - Razorpay
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret
RAZORPAY_WEBHOOK_SECRET=your-razorpay-webhook-secret

# WhatsApp Integration - Twilio
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# GST Network (GSTN) API
GSTN_BASE_URL=https://api.gst.gov.in
GSTN_API_KEY=your-gstn-api-key
GSTN_CLIENT_ID=your-gstn-client-id
GSTN_CLIENT_SECRET=your-gstn-client-secret

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_PATH=uploads/
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,doc,docx,xls,xlsx

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Tally Integration
TALLY_SERVER_HOST=localhost
TALLY_SERVER_PORT=9000
TALLY_COMPANY_PATH=C:\\Tally\\Data
TALLY_SYNC_INTERVAL=300000

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret

# API Keys for External Services
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
SMS_API_KEY=your-sms-api-key
SMS_API_SECRET=your-sms-api-secret

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=backups/

# Monitoring and Analytics
SENTRY_DSN=your-sentry-dsn
GOOGLE_ANALYTICS_ID=your-ga-id

# Development Tools
DEBUG=finsync360:*
SWAGGER_ENABLED=true
