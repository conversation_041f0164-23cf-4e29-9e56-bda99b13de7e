{"name": "finsync360-desktop", "version": "1.0.0", "description": "FinSync360 Desktop Application - Full ERP with Offline Capabilities", "main": "src/main/main.js", "homepage": "./", "scripts": {"electron": "electron .", "electron:dev": "concurrently \"npm run vite:dev\" \"wait-on http://localhost:3002 && electron .\"", "vite:dev": "cd src/renderer && npm run dev", "vite:build": "cd src/renderer && npm run build", "build": "npm run vite:build && electron-builder", "build:win": "npm run vite:build && electron-builder --win", "build:mac": "npm run vite:build && electron-builder --mac", "build:linux": "npm run vite:build && electron-builder --linux", "dist": "npm run build", "pack": "electron-builder --dir", "test": "jest", "lint": "eslint src/", "format": "prettier --write src/", "dev": "npm run electron:dev", "start": "npm run electron:dev", "setup": "npm install && cd src/renderer && npm install"}, "keywords": ["electron", "erp", "accounting", "inventory", "offline", "desktop", "business"], "author": "FinSync360 Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.9.1", "eslint": "^8.56.0", "jest": "^29.7.0", "prettier": "^3.1.1", "wait-on": "^7.2.0"}, "dependencies": {"@types/node": "^20.10.1", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "electron-log": "^5.0.1", "axios": "^1.6.2", "ws": "^8.14.2", "node-cron": "^3.0.3", "uuid": "^9.0.1", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "electron-is-dev": "^2.0.0", "xlsx": "^0.18.5", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0"}, "build": {"appId": "com.finsync360.desktop", "productName": "FinSync360 Desktop", "directories": {"output": "dist", "buildResources": "build"}, "files": ["src/main/**/*", "src/renderer/dist/**/*", "src/database/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "src/database/schema.sql", "to": "database/schema.sql"}], "win": {"target": "nsis", "icon": "build/icon.ico"}, "mac": {"target": "dmg", "icon": "build/icon.icns"}, "linux": {"target": "AppImage", "icon": "build/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}