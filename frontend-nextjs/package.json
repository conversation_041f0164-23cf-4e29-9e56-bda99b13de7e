{"name": "frontend-nextjs", "version": "0.1.0", "private": true, "engines": {"node": "18.x", "npm": "9.x"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint --fix", "heroku-postbuild": "npm run build"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.8.4", "@tanstack/react-table": "^8.10.7", "autoprefixer": "^10.4.21", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^12.18.1", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jspdf": "^2.5.1", "lodash": "^4.17.21", "lucide-react": "^0.522.0", "moment": "^2.29.4", "next": "15.3.3", "numeral": "^2.0.6", "postcss": "^8.5.4", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-datepicker": "^4.25.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-error-boundary": "^4.0.11", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-select": "^5.8.0", "recharts": "^2.8.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.202", "@types/node": "^20", "@types/numeral": "^2.0.5", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8.4.32", "prettier": "^3.1.0", "typescript": "^5"}}