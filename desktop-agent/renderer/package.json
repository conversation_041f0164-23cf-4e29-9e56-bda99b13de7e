{"name": "finsync360-desktop-agent-ui", "version": "1.0.0", "description": "React UI for FinSync360 Desktop Agent", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "react-use": "^17.4.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "prettier": "^3.1.0"}}