version: '3.8'

services:
  # MongoDB Database (Local)
  mongodb:
    image: mongo:6.0
    container_name: finsync360-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: finsync360
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./deployment/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - finsync360-network
    command: mongod --auth --bind_ip_all

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: finsync360-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - finsync360-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: finsync360-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5000
      MONGODB_URI: *********************************************************************
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production
      JWT_EXPIRE: 7d
      JWT_REFRESH_EXPIRE: 30d
      ENCRYPTION_KEY: your-32-character-encryption-key
      BCRYPT_ROUNDS: 12
      LOG_LEVEL: info
    ports:
      - "5000:5000"
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    depends_on:
      - mongodb
      - redis
    networks:
      - finsync360-network

  # Frontend Web App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: finsync360-frontend
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:5000/api
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - finsync360-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: finsync360-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./deployment/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - finsync360-network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  finsync360-network:
    driver: bridge
