{"name": "finsync360-mobile", "version": "1.0.0", "description": "FinSync360 Mobile Application - ERP with Offline Sync", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace FinSync360Mobile.xcworkspace -scheme FinSync360Mobile -configuration Release archive", "clean": "react-native clean-project-auto", "pod-install": "cd ios && pod install", "reset-cache": "react-native start --reset-cache"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.21.0", "@react-native-community/netinfo": "^11.2.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/stack": "^6.3.20", "@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.2", "axios-mock-adapter": "^2.1.0", "react": "18.2.0", "react-hook-form": "^7.48.2", "react-native": "0.73.2", "react-native-biometrics": "^3.0.1", "react-native-chart-kit": "^6.12.0", "react-native-config": "^1.5.1", "react-native-date-picker": "^4.3.3", "react-native-device-info": "^10.11.0", "react-native-document-picker": "^9.1.1", "react-native-encrypted-storage": "^4.0.3", "react-native-fast-image": "^8.6.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.14.0", "react-native-image-picker": "^7.1.0", "react-native-keychain": "^8.1.3", "react-native-paper": "^5.11.6", "react-native-permissions": "^4.1.1", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "^3.6.1", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^3.29.0", "react-native-share": "^10.0.2", "react-native-sqlite-storage": "^6.0.1", "react-native-svg": "^14.1.0", "react-native-toast-message": "^2.2.0", "react-native-vector-icons": "^10.0.3", "react-native-webview": "^13.6.4", "react-redux": "^9.0.4", "redux-persist": "^6.0.0", "socket.io-client": "^4.7.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.73.1", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.9.0", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "0.77.0", "prettier": "^2.8.8", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=18"}, "keywords": ["react-native", "mobile", "erp", "tally-integration", "offline-sync", "business-app"], "author": "FinSync360 Team", "license": "MIT"}