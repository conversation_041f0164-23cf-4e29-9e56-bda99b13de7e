# Environment Configuration
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/finsync360
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRE=7d
JWT_REFRESH_EXPIRE=30d

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key

# Payment Gateway - Razorpay
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret
RAZORPAY_WEBHOOK_SECRET=your-razorpay-webhook-secret

# WhatsApp Integration - Twilio
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# GST Network (GSTN) API
GSTN_BASE_URL=https://api.gst.gov.in
GSTN_API_KEY=your-gstn-api-key
GSTN_CLIENT_ID=your-gstn-client-id
GSTN_CLIENT_SECRET=your-gstn-client-secret

# Tally Integration
TALLY_SERVER_HOST=localhost
TALLY_SERVER_PORT=9000
TALLY_COMPANY_PATH=C:\\Tally\\Data
TALLY_SYNC_INTERVAL=300000

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret

# Logging
LOG_LEVEL=info

# Frontend Configuration
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_APP_NAME=FinSync360
REACT_APP_VERSION=1.0.0
