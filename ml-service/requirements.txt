# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database drivers
motor==3.3.2
pymongo==4.6.0
asyncio-mqtt==0.16.1

# Machine Learning and Data Science
scikit-learn==1.3.2
pandas==2.1.4
numpy==1.24.4
scipy==1.11.4
joblib==1.3.2

# Deep Learning (optional, for advanced models)
tensorflow==2.15.0
torch==2.1.2
transformers==4.36.2

# Data preprocessing and feature engineering
category-encoders==2.6.3
imbalanced-learn==0.11.0
feature-engine==1.6.2

# Time series forecasting
statsmodels==0.14.1
prophet==1.1.5
pmdarima==2.0.4

# Visualization and plotting
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# HTTP client for API calls
httpx==0.25.2
aiohttp==3.9.1

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Configuration and environment
pydantic==2.5.2
pydantic-settings==2.1.0
python-dotenv==1.0.0

# Logging and monitoring
structlog==23.2.0
prometheus-client==0.19.0

# Task scheduling
apscheduler==3.10.4
celery==5.3.4
redis==5.0.1

# Data validation and serialization
marshmallow==3.20.2
cerberus==1.3.5

# Utilities
python-dateutil==2.8.2
pytz==2023.3
click==8.1.7

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# Development tools
black==23.12.0
flake8==6.1.0
isort==5.13.2
mypy==1.8.0

# Production deployment
gunicorn==21.2.0
