{"name": "finsync360-desktop-agent", "version": "1.0.0", "description": "FinSync360 Desktop Agent for Tally ERP Integration", "main": "main.js", "homepage": "./", "private": true, "scripts": {"start": "electron .", "dev": "concurrently \"npm run react:dev\" \"wait-on http://localhost:3001 && electron .\"", "react:dev": "cd renderer && npm run dev", "react:build": "cd renderer && npm run build", "build": "npm run react:build", "electron:dev": "cross-env NODE_ENV=development electron .", "electron:pack": "electron-builder", "electron:dist": "npm run build && electron-builder --publish=never", "electron:publish": "npm run build && electron-builder --publish=always", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/ --ext .js,.jsx", "lint:fix": "eslint src/ --ext .js,.jsx --fix", "install-deps": "electron-builder install-app-deps", "postinstall": "cd renderer && npm install"}, "build": {"appId": "com.finsync360.desktop-agent", "productName": "FinSync360 Desktop Agent", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "renderer/", "src/", "assets/", "node_modules/"], "mac": {"category": "public.app-category.business", "target": "dmg"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}, "publish": {"provider": "github", "owner": "finsync360", "repo": "desktop-agent"}, "autoUpdater": {"enabled": true}}, "dependencies": {"axios": "^1.6.2", "child_process": "^1.0.2", "crypto-js": "^4.2.0", "electron-log": "^5.0.1", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "fast-xml-parser": "^4.3.2", "fs-extra": "^11.2.0", "net": "^1.0.2", "node-cron": "^3.0.3", "node-machine-id": "^1.1.12", "node-notifier": "^10.0.1", "os": "^0.1.2", "path": "^0.12.7", "semver": "^7.5.4", "systeminformation": "^5.21.20", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "ws": "^8.14.2"}, "devDependencies": {"@types/node": "^20.10.4", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^27.3.11", "electron-builder": "^24.13.3", "electron-is-dev": "^2.0.0", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "wait-on": "^7.2.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["electron", "desktop-agent", "tally-integration", "erp", "sync", "offline"], "author": "FinSync360 Team", "license": "MIT"}